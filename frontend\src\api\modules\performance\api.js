/**
 * 履职管理API请求方法
 * 包含履职记录、附件管理、文件上传等接口
 */
import httpClient from '../../http/client'
import { API_CONFIG } from '../../http/config'

/**
 * 履职记录API
 */
export const performanceAPI = {
  /**
   * 获取履职记录列表
   * @param {Object} params 查询参数
   * @param {string} params.start_date 开始日期 (YYYY-MM-DD)
   * @param {string} params.end_date 结束日期 (YYYY-MM-DD)
   * @param {string} params.performance_type 履职类型
   * @param {string} params.performance_status 履职状态
   * @param {boolean} params.has_attachments 是否有附件
   * @param {string} params.search 搜索关键词
   * @param {string} params.ordering 排序字段
   * @param {number} params.page 页码
   * @param {number} params.page_size 每页数量
   * @returns {Promise} 履职记录列表
   */
  getRecordsList(params = {}) {
    return httpClient.get('/performance/records/', { params })
  },

  /**
   * 获取履职记录详情
   * @param {number} recordId 记录ID
   * @returns {Promise} 记录详情
   */
  getRecordDetail(recordId) {
    return httpClient.get(`/performance/records/${recordId}/`)
  },

  /**
   * 创建履职记录
   * @param {Object} recordData 记录数据
   * @param {string} recordData.performance_date 履职日期 (YYYY-MM-DD)
   * @param {string} recordData.performance_type 履职类型
   * @param {string} recordData.performance_content 履职内容
   * @param {string} recordData.activity_location 活动地点
   * @param {string} recordData.detailed_description 详细描述
   * @param {string} recordData.performance_status 履职状态
   * @returns {Promise} 创建结果
   */
  createRecord(recordData) {
    return httpClient.post('/performance/records/', recordData)
  },

  /**
   * 更新履职记录
   * @param {number} recordId 记录ID
   * @param {Object} recordData 更新的记录数据
   * @returns {Promise} 更新结果
   */
  updateRecord(recordId, recordData) {
    return httpClient.put(`/performance/records/${recordId}/`, recordData)
  },

  /**
   * 部分更新履职记录
   * @param {number} recordId 记录ID
   * @param {Object} recordData 更新的记录数据
   * @returns {Promise} 更新结果
   */
  patchRecord(recordId, recordData) {
    return httpClient.patch(`/performance/records/${recordId}/`, recordData)
  },

  /**
   * 删除履职记录
   * @param {number} recordId 记录ID
   * @returns {Promise} 删除结果
   */
  deleteRecord(recordId) {
    return httpClient.delete(`/performance/records/${recordId}/`)
  },

  /**
   * 获取履职记录统计数据
   * @returns {Promise} 统计数据
   */
  getRecordStats() {
    return httpClient.get('/performance/records/stats/')
  },

  /**
   * 导出履职记录数据
   * @param {Object} params 导出参数
   * @returns {Promise} 导出数据
   */
  exportRecords(params = {}) {
    return httpClient.get('/performance/records/export/', { params })
  }
}

/**
 * 履职记录附件API
 */
export const performanceAttachmentAPI = {
  /**
   * 获取履职记录的附件列表
   * @param {number} performanceRecordId 履职记录ID
   * @returns {Promise} 附件列表
   */
  getAttachmentsList(performanceRecordId) {
    return httpClient.get('/performance/attachments/', {
      params: { performance_record_id: performanceRecordId }
    })
  },

  /**
   * 获取附件详情
   * @param {number} attachmentId 附件ID
   * @returns {Promise} 附件详情
   */
  getAttachmentDetail(attachmentId) {
    return httpClient.get(`/performance/attachments/${attachmentId}/`)
  },

  /**
   * 更新附件信息（如排序）
   * @param {number} attachmentId 附件ID
   * @param {Object} attachmentData 更新数据
   * @returns {Promise} 更新结果
   */
  updateAttachment(attachmentId, attachmentData) {
    return httpClient.patch(`/performance/attachments/${attachmentId}/`, attachmentData)
  },

  /**
   * 删除附件
   * @param {number} attachmentId 附件ID
   * @returns {Promise} 删除结果
   */
  deleteAttachment(attachmentId) {
    return httpClient.delete(`/performance/attachments/${attachmentId}/`)
  }
}

/**
 * 文件上传API
 */
export const performanceFileAPI = {
  /**
   * 上传文件
   * @param {Object} uploadData 上传数据
   * @param {number} uploadData.performance_record_id 履职记录ID
   * @param {string} uploadData.file_type 文件类型 (image|audio|video|document)
   * @param {File} uploadData.file 文件对象
   * @param {Function} onUploadProgress 上传进度回调
   * @returns {Promise} 上传结果
   */
  uploadFile(uploadData, onUploadProgress) {
    const formData = new FormData()
    formData.append('performance_record_id', uploadData.performance_record_id)
    formData.append('file_type', uploadData.file_type)
    formData.append('file', uploadData.file)

    return httpClient.post('/performance/upload/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress
    })
  },

  /**
   * 获取上传URL（用于组件）
   * @returns {string} 上传URL
   */
  getUploadUrl() {
    return `${API_CONFIG.BASE_URL}/performance/upload/`
  },

  /**
   * 使用Fetch API安全获取文件Blob
   * @param {number} attachmentId 附件ID
   * @returns {Promise<Blob>} 文件Blob
   */
  async fetchFileAsBlob(attachmentId) {
    try {
      const token = localStorage.getItem('access_token')
      if (!token) {
        throw new Error('用户未登录')
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}/performance/secure-file/${attachmentId}/`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        }
      })

      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('用户认证失败')
        } else if (response.status === 403) {
          throw new Error('无权访问此文件')
        } else if (response.status === 404) {
          throw new Error('文件不存在')
        } else {
          throw new Error(`文件获取失败: ${response.status}`)
        }
      }

      return await response.blob()
    } catch (error) {
      console.error('获取文件失败:', error)
      throw error
    }
  },

  /**
   * 获取安全文件的Blob URL（用于预览和下载）
   * @param {number} attachmentId 附件ID
   * @returns {Promise<string>} Blob URL
   */
  async getSecureFileBlobUrl(attachmentId) {
    try {
      const blob = await this.fetchFileAsBlob(attachmentId)
      return URL.createObjectURL(blob)
    } catch (error) {
      console.error('获取文件Blob URL失败:', error)
      throw error
    }
  },

  /**
   * 下载附件文件
   * @param {number} attachmentId 附件ID
   * @param {string} filename 文件名
   * @returns {Promise} 下载结果
   */
  async downloadAttachment(attachmentId, filename) {
    try {
      const blob = await this.fetchFileAsBlob(attachmentId)
      
      // 创建下载链接
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename || `attachment_${attachmentId}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      return { success: true }
    } catch (error) {
      console.error('下载附件失败:', error)
      throw error
    }
  },

  /**
   * 清理孤立文件（管理员专用）
   * @returns {Promise} 清理结果
   */
  cleanupFiles() {
    return httpClient.post('/performance/cleanup/')
  }
}

/**
 * 选择项数据API
 */
export const performanceChoicesAPI = {
  /**
   * 获取履职类型选择项
   * @returns {Promise} 类型选择项列表
   */
  getPerformanceTypes() {
    return httpClient.get('/performance/choices/types/')
  },

  /**
   * 获取履职状态选择项
   * @returns {Promise} 状态选择项列表
   */
  getPerformanceStatus() {
    return httpClient.get('/performance/choices/status/')
  },

  /**
   * 获取文件类型限制
   * @returns {Promise} 文件类型限制配置
   */
  getFileTypeLimits() {
    return httpClient.get('/performance/choices/file-limits/')
  }
}

/**
 * 工作人员专用履职管理API
 */
export const staffPerformanceAPI = {
  /**
   * 获取代表履职概览列表（工作人员专用）
   * @param {Object} params 查询参数
   * @param {string} params.time_filter 时间筛选 (month/quarter/year)
   * @param {string} params.performance_type 履职类型筛选
   * @param {string} params.search 搜索代表姓名
   * @returns {Promise} 代表履职概览列表
   */
  getRepresentativesList(params = {}) {
    return httpClient.get('/performance/staff/representatives/', { params })
  },

  /**
   * 获取履职概览统计（工作人员专用）
   * @returns {Promise} 履职概览统计数据
   */
  getPerformanceOverview() {
    return httpClient.get('/performance/staff/overview/')
  },

  /**
   * 获取指定代表的履职记录列表（工作人员专用）
   * @param {number} representativeId 代表ID
   * @param {Object} params 查询参数
   * @returns {Promise} 代表履职记录列表
   */
  getRepresentativeRecords(representativeId, params = {}) {
    return httpClient.get(`/performance/staff/representatives/${representativeId}/records/`, { params })
  },

  /**
   * 获取指定代表的基本信息（工作人员专用）
   * @param {number} representativeId 代表ID
   * @returns {Promise} 代表基本信息
   */
  getRepresentativeInfo(representativeId) {
    return httpClient.get(`/performance/staff/representatives/${representativeId}/`)
  },

  /**
   * 获取履职记录详情（工作人员专用）
   * @param {number} recordId 记录ID
   * @returns {Promise} 记录详情
   */
  getRecordDetail(recordId) {
    return httpClient.get(`/performance/staff/records/${recordId}/`)
  }
}