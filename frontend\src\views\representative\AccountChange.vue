<template>
  <div class="account-change-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>账号管理</h2>
      <p>管理您的账号信息，包括用户名和密码</p>
    </div>

    <!-- 当前用户信息显示 -->
    <el-card class="user-info-card">
      <template #header>
        <div class="card-header">
          <span>当前账号信息</span>
        </div>
      </template>
      <div class="user-info">
        <div class="info-item">
          <span class="label">当前用户名：</span>
          <span class="value">{{ currentUsername }}</span>
        </div>
        <div class="info-item">
          <span class="label">用户角色：</span>
          <span class="value">{{ userRoleText }}</span>
        </div>
      </div>
    </el-card>

    <!-- Tab切换 -->
    <el-card class="change-form-card">
      <el-tabs v-model="activeTab" class="account-tabs">
        <!-- 密码修改Tab -->
        <el-tab-pane label="密码修改" name="password">
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordFormRules"
            label-width="120px"
            size="large"
            @submit.prevent="submitPasswordForm"
          >
            <el-form-item label="当前密码" prop="currentPassword">
              <el-input
                v-model="passwordForm.currentPassword"
                type="password"
                placeholder="请输入当前密码"
                show-password
                :disabled="passwordLoading"
              />
            </el-form-item>

            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
                :disabled="passwordLoading"
              />
              <div class="password-tips">
                <p>密码要求：</p>
                <ul>
                  <li>长度至少8位</li>
                  <li>建议包含字母和数字</li>
                  <li>不建议使用纯数字或纯字母</li>
                </ul>
              </div>
            </el-form-item>

            <el-form-item label="确认新密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                show-password
                :disabled="passwordLoading"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="passwordLoading"
                @click="submitPasswordForm"
              >
                {{ passwordLoading ? '修改中...' : '修改密码' }}
              </el-button>
              <el-button @click="resetPasswordForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 用户名修改Tab -->
        <el-tab-pane label="用户名修改" name="username">
          <el-form
            ref="usernameFormRef"
            :model="usernameForm"
            :rules="usernameFormRules"
            label-width="120px"
            size="large"
            @submit.prevent="submitUsernameForm"
          >
            <el-form-item label="当前密码" prop="currentPassword">
              <el-input
                v-model="usernameForm.currentPassword"
                type="password"
                placeholder="请输入当前密码以验证身份"
                show-password
                :disabled="usernameLoading"
              />
            </el-form-item>

            <el-form-item label="新用户名" prop="newUsername">
              <el-input
                v-model="usernameForm.newUsername"
                placeholder="请输入新用户名"
                :disabled="usernameLoading"
              />
              <div class="username-tips">
                <p>用户名要求：</p>
                <ul>
                  <li>长度3-50个字符</li>
                  <li>只能包含字母、数字和下划线</li>
                  <li>不能与其他用户重复</li>
                </ul>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="usernameLoading"
                @click="submitUsernameForm"
              >
                {{ usernameLoading ? '修改中...' : '修改用户名' }}
              </el-button>
              <el-button @click="resetUsernameForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 安全提示 -->
    <el-card class="security-tips-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>安全提示</span>
        </div>
      </template>
      <div class="security-tips">
        <el-alert
          title="安全建议"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            <ul>
              <li>定期更换密码，建议每3-6个月更换一次</li>
              <li>不要在多个系统中使用相同密码</li>
              <li>密码修改后，系统将自动退出，需要重新登录</li>
              <li>用户名修改后，请使用新用户名登录</li>
              <li>如遇问题，请联系站点工作人员</li>
            </ul>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import { authAPI } from '@/api'

const router = useRouter()
const userStore = useUserStore()

// 当前Tab
const activeTab = ref('password')

// 表单引用
const passwordFormRef = ref()
const usernameFormRef = ref()

// 加载状态
const passwordLoading = ref(false)
const usernameLoading = ref(false)

// 计算属性
const currentUsername = computed(() => userStore.userInfo?.username || '未知')
const userRoleText = computed(() => userStore.roleText || '未知角色')

// 密码修改表单数据
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 用户名修改表单数据
const usernameForm = reactive({
  currentPassword: '',
  newUsername: ''
})

// 密码修改表单验证规则
const passwordFormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, message: '密码长度至少8位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 用户名修改表单验证规则
const usernameFormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newUsername: [
    { required: true, message: '请输入新用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在3到50个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ]
}

// 提交密码修改表单
const submitPasswordForm = async () => {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()
    passwordLoading.value = true

    // 调用密码修改API
    const response = await authAPI.changePassword({
      old_password: passwordForm.currentPassword,
      new_password: passwordForm.newPassword,
      confirm_password: passwordForm.confirmPassword
    })

    const responseData = response.data
    
    if (responseData.success) {
      ElMessage.success(responseData.message || '密码修改成功！系统将自动退出，请重新登录')
      
      // 清空表单
      resetPasswordForm()
      
      // 延迟退出登录
      setTimeout(async () => {
        await userStore.logout()
        router.push('/login')
      }, 2000)
    } else {
      ElMessage.error(responseData.message || '密码修改失败，请重试')
    }

  } catch (error) {
    console.error('密码修改失败:', error)
    handleApiError(error, '密码修改失败')
  } finally {
    passwordLoading.value = false
  }
}

// 提交用户名修改表单
const submitUsernameForm = async () => {
  if (!usernameFormRef.value) return

  try {
    await usernameFormRef.value.validate()
    usernameLoading.value = true

    // 调用用户名修改API
    const response = await authAPI.changeUsername({
      current_password: usernameForm.currentPassword,
      new_username: usernameForm.newUsername
    })

    const responseData = response.data
    
    if (responseData.success) {
      ElMessage.success(responseData.message || '用户名修改成功！系统将自动退出，请使用新用户名登录')
      
      // 清空表单
      resetUsernameForm()
      
      // 延迟退出登录
      setTimeout(async () => {
        await userStore.logout()
        router.push('/login')
      }, 2000)
    } else {
      ElMessage.error(responseData.message || '用户名修改失败，请重试')
    }

  } catch (error) {
    console.error('用户名修改失败:', error)
    handleApiError(error, '用户名修改失败')
  } finally {
    usernameLoading.value = false
  }
}

// 处理API错误
const handleApiError = (error, defaultMessage) => {
  if (error.response && error.response.data) {
    const errorData = error.response.data
    if (errorData.errors) {
      // 处理字段验证错误
      const errorMessages = []
      Object.keys(errorData.errors).forEach(field => {
        const fieldErrors = errorData.errors[field]
        if (Array.isArray(fieldErrors)) {
          errorMessages.push(...fieldErrors)
        } else {
          errorMessages.push(fieldErrors)
        }
      })
      ElMessage.error(errorMessages.join('；') || defaultMessage)
    } else {
      ElMessage.error(errorData.message || defaultMessage)
    }
  } else {
    ElMessage.error(defaultMessage)
  }
}

// 重置密码表单
const resetPasswordForm = () => {
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
}

// 重置用户名表单
const resetUsernameForm = () => {
  if (usernameFormRef.value) {
    usernameFormRef.value.resetFields()
  }
  Object.assign(usernameForm, {
    currentPassword: '',
    newUsername: ''
  })
}
</script>

<style scoped>
.account-change-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  color: var(--china-red);
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.user-info-card {
  margin-bottom: 20px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #333;
  min-width: 100px;
}

.info-item .value {
  color: var(--china-red);
  font-weight: 600;
}

.change-form-card {
  margin-bottom: 20px;
}

.account-tabs {
  margin-top: 16px;
}

.password-tips,
.username-tips {
  margin-top: 8px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid var(--china-red);
}

.password-tips p,
.username-tips p {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.password-tips ul,
.username-tips ul {
  margin: 0;
  padding-left: 16px;
}

.password-tips li,
.username-tips li {
  font-size: 12px;
  line-height: 1.5;
  margin-bottom: 4px;
  color: #666;
}

.security-tips ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.security-tips li {
  font-size: 13px;
  line-height: 1.6;
  margin-bottom: 6px;
  color: #666;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-button--primary) {
  background-color: var(--china-red);
  border-color: var(--china-red);
}

:deep(.el-button--primary:hover) {
  background-color: #a52525;
  border-color: #a52525;
}

:deep(.el-tabs__item) {
  font-weight: 500;
}

:deep(.el-tabs__item.is-active) {
  color: var(--china-red);
}

:deep(.el-tabs__active-bar) {
  background-color: var(--china-red);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .account-change-container {
    padding: 16px;
  }

  :deep(.el-form-item__label) {
    width: 100px !important;
  }

  .page-header h2 {
    font-size: 20px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .info-item .label {
    min-width: auto;
  }
}
</style>
