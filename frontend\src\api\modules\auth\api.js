/**
 * 用户认证API请求方法
 * 包含登录、登出、token刷新、用户信息等接口
 */
import request from '../../http/client'
import { AUTH_ENDPOINTS } from '../../http/config'

/**
 * 用户认证相关API
 */
export const authAPI = {
  /**
   * 用户登录
   * @param {Object} loginData - 登录数据
   * @param {string} loginData.username - 用户名
   * @param {string} loginData.password - 密码
   * @returns {Promise} 登录结果
   */
  login(loginData) {
    return request({
      url: AUTH_ENDPOINTS.LOGIN,
      method: 'post',
      data: loginData,
      showLoading: false  // 禁用全局Loading，使用登录按钮的loading状态
    })
  },

  /**
   * 用户登出
   * @param {Object} logoutData - 登出数据
   * @param {string} logoutData.refresh_token - 刷新令牌
   * @returns {Promise} 登出结果
   */
  logout(logoutData) {
    return request({
      url: AUTH_ENDPOINTS.LOGOUT,
      method: 'post',
      data: logoutData,
      showLoading: false
    })
  },

  /**
   * 刷新访问令牌
   * @param {Object} refreshData - 刷新数据
   * @param {string} refreshData.refresh - 刷新令牌
   * @returns {Promise} 新的访问令牌
   */
  refreshToken(refreshData) {
    return request({
      url: AUTH_ENDPOINTS.REFRESH_TOKEN,
      method: 'post',
      data: refreshData,
      showLoading: false
    })
  },

  /**
   * 获取当前用户信息
   * @returns {Promise} 用户信息
   */
  getUserProfile() {
    return request({
      url: AUTH_ENDPOINTS.USER_PROFILE,
      method: 'get',
      showLoading: false
    })
  },

  /**
   * 更新用户个人资料
   * @param {Object} profileData - 个人资料数据
   * @returns {Promise} 更新结果
   */
  updateProfile(profileData) {
    return request({
      url: AUTH_ENDPOINTS.USER_PROFILE,
      method: 'put',
      data: profileData,
      showLoading: true
    })
  },

  /**
   * 修改密码
   * @param {Object} passwordData - 密码数据
   * @param {string} passwordData.old_password - 旧密码
   * @param {string} passwordData.new_password - 新密码
   * @param {string} passwordData.confirm_password - 确认新密码
   * @returns {Promise} 修改结果
   */
  changePassword(passwordData) {
    return request({
      url: AUTH_ENDPOINTS.CHANGE_PASSWORD,
      method: 'post',
      data: passwordData,
      showLoading: true
    })
  },

  /**
   * 修改用户名
   * @param {Object} usernameData - 用户名数据
   * @param {string} usernameData.current_password - 当前密码
   * @param {string} usernameData.new_username - 新用户名
   * @returns {Promise} 修改结果
   */
  changeUsername(usernameData) {
    return request({
      url: AUTH_ENDPOINTS.CHANGE_USERNAME,
      method: 'post',
      data: usernameData,
      showLoading: true
    })
  }
}

/**
 * 用户管理API（工作人员使用）
 */
export const userManagementAPI = {
  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.page_size - 每页数量
   * @param {string} params.search - 搜索关键词
   * @param {string} params.role - 角色筛选
   * @returns {Promise} 用户列表
   */
  getUserList(params = {}) {
    return request({
      url: AUTH_ENDPOINTS.USER_LIST,
      method: 'get',
      params,
      showLoading: true
    })
  },

  /**
   * 获取用户详情
   * @param {number} userId - 用户ID
   * @returns {Promise} 用户详情
   */
  getUserDetail(userId) {
    const url = AUTH_ENDPOINTS.USER_DETAIL.replace(':id', userId)
    return request({
      url,
      method: 'get',
      showLoading: true
    })
  },

  /**
   * 创建新用户
   * @param {Object} userData - 用户数据
   * @returns {Promise} 创建结果
   */
  createUser(userData) {
    return request({
      url: AUTH_ENDPOINTS.USER_LIST,
      method: 'post',
      data: userData,
      showLoading: true
    })
  },

  /**
   * 创建账号（包含角色相关信息）
   * @param {Object} accountData - 账号数据
   * @returns {Promise} 创建结果
   */
  createAccount(accountData) {
    return request({
      url: AUTH_ENDPOINTS.USER_LIST,
      method: 'post',
      data: accountData,
      showLoading: true
    })
  },

  /**
   * 更新用户信息
   * @param {number} userId - 用户ID
   * @param {Object} userData - 用户数据
   * @returns {Promise} 更新结果
   */
  updateUser(userId, userData) {
    const url = AUTH_ENDPOINTS.USER_DETAIL.replace(':id', userId)
    return request({
      url,
      method: 'put',
      data: userData,
      showLoading: true
    })
  },

  /**
   * 部分更新用户信息
   * @param {number} userId - 用户ID
   * @param {Object} userData - 部分用户数据
   * @returns {Promise} 更新结果
   */
  patchUser(userId, userData) {
    const url = AUTH_ENDPOINTS.USER_DETAIL.replace(':id', userId)
    return request({
      url,
      method: 'patch',
      data: userData,
      showLoading: true
    })
  },

  /**
   * 删除用户
   * @param {number} userId - 用户ID
   * @returns {Promise} 删除结果
   */
  deleteUser(userId) {
    const url = AUTH_ENDPOINTS.USER_DETAIL.replace(':id', userId)
    return request({
      url,
      method: 'delete',
      showLoading: true
    })
  },

  /**
   * 重置用户密码
   * @param {number} userId - 用户ID
   * @param {Object} passwordData - 密码数据
   * @param {string} passwordData.new_password - 新密码
   * @param {string} passwordData.confirm_password - 确认新密码
   * @returns {Promise} 重置结果
   */
  resetUserPassword(userId, passwordData) {
    const url = AUTH_ENDPOINTS.USER_DETAIL.replace(':id', userId)
    return request({
      url,
      method: 'put',
      data: passwordData,
      showLoading: true
    })
  },

  /**
   * 获取人大代表列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 代表列表
   */
  getRepresentativeList(params = {}) {
    return request({
      url: AUTH_ENDPOINTS.REPRESENTATIVE_LIST,
      method: 'get',
      params,
      showLoading: true
    })
  },

  /**
   * 获取工作人员列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 工作人员列表
   */
  getStaffList(params = {}) {
    return request({
      url: AUTH_ENDPOINTS.STAFF_LIST,
      method: 'get',
      params,
      showLoading: true
    })
  },

  /**
   * 批量操作用户
   * @param {string} action - 操作类型 (activate, deactivate, delete)
   * @param {Array} userIds - 用户ID数组
   * @returns {Promise} 操作结果
   */
  batchUpdateUsers(action, userIds) {
    return request({
      url: `${AUTH_ENDPOINTS.USER_LIST}batch/`,
      method: 'post',
      data: {
        action,
        user_ids: userIds
      },
      showLoading: true
    })
  },

  /**
   * 导出用户列表
   * @param {Object} params - 查询参数
   * @param {string} params.role - 角色筛选
   * @param {boolean} params.is_active - 状态筛选
   * @returns {Promise} 文件下载
   */
  exportUsers(params = {}) {
    return request({
      url: '/users/export/',
      method: 'get',
      params,
      responseType: 'blob',
      showLoading: true
    })
  },

  /**
   * 导入用户列表
   * @param {File} file - Excel文件
   * @returns {Promise} 导入结果
   */
  importUsers(file) {
    const formData = new FormData()
    formData.append('file', file)

    return request({
      url: '/users/import/',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      showLoading: true
    })
  },

  /**
   * 下载导入模板
   * @param {string} role - 角色类型 (representative/staff)
   * @returns {Promise} 模板文件
   */
  downloadTemplate(role) {
    return request({
      url: '/users/template/',
      method: 'get',
      params: { role },
      responseType: 'blob',
      showLoading: true
    })
  }
}