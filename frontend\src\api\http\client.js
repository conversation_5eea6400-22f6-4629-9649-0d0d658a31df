/**
 * HTTP客户端配置
 * 创建和配置axios实例
 */
import axios from 'axios'
import { API_CONFIG } from './config'

/**
 * 创建axios实例
 */
const createHttpClient = () => {
  const client = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })

  return client
}

// 创建HTTP客户端实例
const httpClient = createHttpClient()

// 延迟导入拦截器以避免循环依赖
import('./interceptors').then(({ setupInterceptors }) => {
  setupInterceptors(httpClient)
})

// 导出默认的HTTP客户端实例
export default httpClient

// 导出创建客户端的工厂函数，用于创建自定义配置的客户端
export { createHttpClient }