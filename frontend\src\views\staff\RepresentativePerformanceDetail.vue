<template>
  <div class="representative-detail-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="goBack" link class="back-btn">
        <el-icon><ArrowLeft /></el-icon>
        返回代表列表
      </el-button>
      <div class="header-info" v-if="representativeInfo">
        <h2>{{ representativeInfo.name }} 的履职记录</h2>
        <div class="rep-meta">
          <el-tag type="primary">{{ representativeInfo.level }}</el-tag>
          <span class="position">{{ representativeInfo.current_position }}</span>
        </div>
      </div>
    </div>



    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-card>
        <el-form :inline="true" :model="searchParams" class="filter-form">
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            />
          </el-form-item>
          <el-form-item label="履职类型">
            <el-select
              v-model="searchParams.performance_type"
              placeholder="选择类型"
              clearable
              @change="loadRecords"
              style="width: 200px;"
            >
              <el-option
                v-for="type in performanceTypes"
                :key="`detail-type-${type.value}`"
                :label="type.label"
                :value="type.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="履职状态">
            <el-select
              v-model="searchParams.performance_status"
              placeholder="选择状态"
              clearable
              @change="loadRecords"
              style="width: 200px;"
            >
              <el-option
                v-for="status in performanceStatus"
                :key="`detail-status-${status.value}`"
                :label="status.label"
                :value="status.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input 
              v-model="searchParams.search" 
              placeholder="搜索履职内容" 
              clearable 
              @input="handleSearchChange"
              style="width: 200px;">
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 履职记录列表 -->
    <div class="records-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <span>履职记录列表</span>
            <el-tag type="info">共 {{ total }} 条记录</el-tag>
          </div>
        </template>
        
        <div v-loading="loading">
          <!-- 记录卡片列表 -->
          <div class="records-list">
            <div 
              v-for="record in recordsList" 
              :key="record.id"
              class="record-card"
              @click="viewRecordDetail(record)"
            >
              <div class="record-header">
                <div class="record-title">
                  {{ record.content_preview || '无履职内容' }}
                </div>
                <div class="record-meta">
                  <el-tag size="small" :type="getStatusColor(record.performance_status)">
                    {{ record.performance_status }}
                  </el-tag>
                  <span class="record-date">{{ record.performance_date }}</span>
                </div>
              </div>
              <div class="record-info">
                <div class="info-item">
                  <el-icon><Calendar /></el-icon>
                  <span>{{ record.performance_type }}</span>
                </div>
                <div class="info-item">
                  <el-icon><Location /></el-icon>
                  <span>{{ record.activity_location }}</span>
                </div>
                <div class="info-item" v-if="record.has_attachments">
                  <el-icon><Paperclip /></el-icon>
                  <span>包含附件</span>
                </div>
              </div>
              <!-- 详细描述 -->
              <div class="record-description" v-if="record.detailed_description">
                <div class="description-label">详细描述：</div>
                <div class="description-text">
                  {{ record.detailed_description }}
                </div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper" v-if="total > 0">
            <el-pagination
              v-model:current-page="searchParams.page"
              v-model:page-size="searchParams.page_size"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>

          <!-- 空状态 -->
          <el-empty v-if="!loading && recordsList.length === 0" description="暂无履职记录">
            <el-button type="primary" @click="loadRecords">重新加载</el-button>
          </el-empty>
        </div>
      </el-card>
    </div>

    <!-- 记录详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      :title="currentRecord?.performance_content"
      width="80%"
      :before-close="closeDetailDialog"
    >
      <div v-if="currentRecord" class="record-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="履职日期">{{ currentRecord.performance_date }}</el-descriptions-item>
            <el-descriptions-item label="履职类型">{{ currentRecord.performance_type }}</el-descriptions-item>
            <el-descriptions-item label="活动地点">{{ currentRecord.activity_location }}</el-descriptions-item>
            <el-descriptions-item label="履职状态">
              <el-tag :type="getStatusColor(currentRecord.performance_status)">
                {{ currentRecord.performance_status }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="履职内容" :span="2">{{ currentRecord.performance_content }}</el-descriptions-item>
            <el-descriptions-item label="详细描述" :span="2" v-if="currentRecord.detailed_description">
              {{ currentRecord.detailed_description }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 附件展示 -->
        <div class="detail-section" v-if="currentRecord.attachments && currentRecord.attachments.length > 0">
          <h3>相关附件</h3>
          <div class="attachments-grid">
            <div 
              v-for="attachment in currentRecord.attachments" 
              :key="attachment.id"
              class="attachment-item"
            >
              <div class="attachment-preview">
                <div
                  v-if="attachment.file_type === 'image'"
                  class="image-container"
                  @click="previewImage(attachment)"
                >
                  <img
                    v-if="attachment.previewUrl"
                    :src="attachment.previewUrl"
                    :alt="attachment.original_filename"
                    class="attachment-image"
                    @error="onImageError(attachment)"
                  />
                  <div v-else-if="attachment.loading" class="image-loading">
                    <el-icon class="is-loading" size="30"><Loading /></el-icon>
                    <p>加载中...</p>
                  </div>
                  <div v-else class="image-error">
                    <el-icon size="30"><Picture /></el-icon>
                    <p>加载失败</p>
                  </div>
                </div>
                <div v-else class="file-icon" @click="downloadAttachment(attachment)">
                  <el-icon size="32">
                    <Document v-if="attachment.file_type === 'document'" />
                    <VideoPlay v-else-if="attachment.file_type === 'video'" />
                    <Picture v-else-if="attachment.file_type === 'audio'" />
                    <Picture v-else />
                  </el-icon>
                </div>
              </div>
              <div class="attachment-info">
                <div class="filename" :title="attachment.original_filename">{{ attachment.original_filename }}</div>
                <div class="filesize">{{ formatFileSize(attachment.file_size) }}</div>
                <el-button
                  v-if="attachment.file_type !== 'image'"
                  size="small"
                  type="primary"
                  plain
                  @click="downloadAttachment(attachment)"
                >
                  下载
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="imagePreviewVisible"
      title="图片预览"
      width="80vw"
      append-to-body
      :close-on-click-modal="true"
    >
      <div class="preview-container">
        <img
          :src="previewImageUrl"
          alt="预览图片"
          class="preview-image"
          v-show="!imageLoading"
        />
        <div v-show="imageLoading" class="preview-loading">
          <el-icon class="is-loading" size="50"><Loading /></el-icon>
          <p>加载中...</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { performanceAPI, staffPerformanceAPI, performanceChoicesAPI, performanceFileAPI } from '@/api/modules/performance'
import {
  ArrowLeft,
  Calendar,
  Location,
  Paperclip,
  Search,
  Document,
  VideoPlay,
  Picture,
  Loading
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 获取代表ID
const representativeId = route.params.id

// 响应式数据
const loading = ref(false)
const representativeInfo = ref(null)
const recordsList = ref([])
const total = ref(0)
const performanceTypes = ref([])
const performanceStatus = ref([])
const dateRange = ref([])
const detailDialogVisible = ref(false)
const currentRecord = ref(null)

// 图片预览状态
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')
const imageLoading = ref(false)

// 搜索参数
const searchParams = reactive({
  page: 1,
  page_size: 20,
  start_date: '',
  end_date: '',
  performance_type: '',
  performance_status: '',
  search: ''
})

// 搜索防抖
let searchTimeout = null

// 方法
const goBack = () => {
  router.push('/staff/representative-performance')
}

const loadRepresentativeInfo = async () => {
  try {
    const response = await staffPerformanceAPI.getRepresentativeInfo(representativeId)
    representativeInfo.value = response.data
  } catch (error) {
    console.error('加载代表信息失败:', error)
    ElMessage.error('加载代表信息失败')
  }
}

const loadRecords = async () => {
  try {
    loading.value = true
    const response = await staffPerformanceAPI.getRepresentativeRecords(representativeId, searchParams)
    recordsList.value = response.data.results || []
    total.value = response.data.count || 0
  } catch (error) {
    console.error('加载履职记录失败:', error)
    ElMessage.error('加载履职记录失败')
  } finally {
    loading.value = false
  }
}

const loadChoices = async () => {
  try {
    const [typesRes, statusRes] = await Promise.all([
      performanceChoicesAPI.getPerformanceTypes(),
      performanceChoicesAPI.getPerformanceStatus()
    ])
    performanceTypes.value = typesRes.data.choices || []
    performanceStatus.value = statusRes.data.choices || []
  } catch (error) {
    console.error('加载选择项失败:', error)
  }
}

const handleDateRangeChange = (dates) => {
  if (dates && dates.length === 2) {
    searchParams.start_date = dates[0]
    searchParams.end_date = dates[1]
  } else {
    searchParams.start_date = ''
    searchParams.end_date = ''
  }
  loadRecords()
}

const handleSearchChange = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    searchParams.page = 1
    loadRecords()
  }, 500)
}

const handleSizeChange = (size) => {
  searchParams.page_size = size
  searchParams.page = 1
  loadRecords()
}

const handleCurrentChange = (page) => {
  searchParams.page = page
  loadRecords()
}

const viewRecordDetail = async (record) => {
  try {
    const response = await staffPerformanceAPI.getRecordDetail(record.id)
    currentRecord.value = response.data

    // 自动加载图片预览
    if (currentRecord.value.attachments) {
      await loadImagePreviews(currentRecord.value.attachments)
    }

    detailDialogVisible.value = true
  } catch (error) {
    console.error('加载记录详情失败:', error)
    ElMessage.error('加载记录详情失败')
  }
}

const closeDetailDialog = () => {
  detailDialogVisible.value = false
  currentRecord.value = null
}

const getStatusColor = (status) => {
  const colorMap = {
    '已完成': 'success',
    '进行中': 'warning',
    '已暂停': 'info'
  }
  return colorMap[status] || 'info'
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 加载图片预览
const loadImagePreviews = async (attachments) => {
  const imageAttachments = attachments.filter(att => att.file_type === 'image')

  for (const attachment of imageAttachments) {
    try {
      attachment.loading = true
      const blobUrl = await performanceFileAPI.getSecureFileBlobUrl(attachment.id)
      attachment.previewUrl = blobUrl
      attachment.loading = false
    } catch (error) {
      console.error(`加载图片预览失败 (ID: ${attachment.id}):`, error)
      attachment.loading = false
      attachment.previewUrl = null
    }
  }
}

// 单个图片加载失败处理
const onImageError = (attachment) => {
  attachment.loading = false
  attachment.previewUrl = null
}

// 预览图片
const previewImage = async (attachment) => {
  try {
    // 如果已经有预览URL，直接使用，不重新请求
    if (attachment.previewUrl) {
      previewImageUrl.value = attachment.previewUrl
      imageLoading.value = false
      imagePreviewVisible.value = true
      return
    }

    // 如果没有预览URL，才进行网络请求
    imageLoading.value = true
    ElMessage.info('正在加载图片预览...')

    const blobUrl = await performanceFileAPI.getSecureFileBlobUrl(attachment.id)
    previewImageUrl.value = blobUrl
    attachment.previewUrl = blobUrl // 缓存URL避免重复请求
    imageLoading.value = false
    imagePreviewVisible.value = true
  } catch (error) {
    console.error('预览图片失败:', error)
    imageLoading.value = false
    ElMessage.error('预览图片失败')
  }
}

const downloadAttachment = async (attachment) => {
  try {
    await performanceFileAPI.downloadAttachment(attachment.id, attachment.original_filename)
    ElMessage.success('文件下载成功')
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}



// 生命周期
onMounted(() => {
  loadRepresentativeInfo()
  loadRecords()
  loadChoices()
})
</script>

<style scoped>
.representative-detail-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.back-btn {
  margin-bottom: 10px;
  color: var(--china-red);
}

.back-btn:hover {
  color: var(--china-red);
  background-color: rgba(200, 16, 46, 0.1);
}

.header-info h2 {
  margin: 0 0 8px 0;
  color: var(--text-color);
  font-size: 24px;
  font-weight: 600;
}

.rep-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.position {
  color: #666;
  font-size: 14px;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-form :deep(.el-form-item) {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.record-card {
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.record-card:hover {
  border-color: var(--china-red);
  box-shadow: 0 4px 12px rgba(200, 16, 46, 0.2);
  transform: translateY(-2px);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.record-title {
  font-size: 15px;
  font-weight: 600;
  color: var(--china-red);
  flex: 1;
  margin-right: 16px;
  line-height: 1.4;
  /* CSS文本截断 */
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.record-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.record-date {
  font-size: 13px;
  color: #666;
}

.record-info {
  display: flex;
  gap: 20px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #666;
}

.record-description {
  margin-bottom: 8px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 6px;
}

.description-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.description-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
  /* 多行文本截断 */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  color: var(--text-color);
  font-size: 16px;
  font-weight: 600;
}

.attachments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
}

.attachment-item {
  text-align: center;
}

.attachment-preview {
  width: 100%;
  height: 100px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  overflow: hidden;
  cursor: pointer;
}

.attachment-preview :deep(.el-image) {
  width: 100%;
  height: 100%;
}

.attachment-preview :deep(.el-image__inner) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-icon {
  color: #909399;
  cursor: pointer;
  transition: color 0.3s;
}

.file-icon:hover {
  color: var(--china-red);
}

.image-container {
  width: 100%;
  height: 100%;
  cursor: pointer;
  overflow: hidden;
}

.attachment-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.image-container:hover .attachment-image {
  transform: scale(1.05);
}

.image-loading,
.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 12px;
}

.image-loading .el-icon,
.image-error .el-icon {
  margin-bottom: 4px;
}

.image-loading .is-loading {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.image-container {
  width: 100%;
  height: 100%;
  cursor: pointer;
  overflow: hidden;
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 12px;
}

.loading-placeholder .el-icon {
  margin-bottom: 4px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.attachment-info {
  font-size: 12px;
}

.filename {
  color: var(--text-color);
  margin-bottom: 4px;
  word-break: break-all;
}

.filesize {
  color: #909399;
}

.preview-container {
  text-align: center;
  position: relative;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  width: 100%;
  max-height: 75vh;
  object-fit: contain;
  border-radius: 8px;
}

.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 14px;
  height: 300px;
}

.preview-loading .el-icon {
  margin-bottom: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .representative-detail-page {
    padding: 10px;
  }

  .record-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .record-meta {
    align-self: flex-end;
  }
  
  .record-info {
    gap: 12px;
  }
  
  .filter-form {
    flex-direction: column;
  }
  
  .filter-form :deep(.el-form-item) {
    margin-bottom: 15px;
  }
}
</style>
