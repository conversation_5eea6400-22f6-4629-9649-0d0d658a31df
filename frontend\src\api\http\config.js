/**
 * API配置文件
 * 定义API基础配置、端点和错误消息
 * 统一管理所有URL配置，方便部署环境切换
 */

// 环境配置 - 统一管理所有URL
export const ENVIRONMENT_CONFIG = {
  // 开发环境配置
  development: {
    // 后端实际地址（用于代理目标）
    BACKEND_URL: 'http://localhost:8000',
    // 前端使用的API地址（通过代理访问）
    API_BASE_URL: '/api/v1',  // 使用相对路径，通过Vite代理
    // 前端地址
    FRONTEND_URL: 'http://localhost:3000',
    USE_PROXY: true  // 开发环境使用代理
  },
  
  // 生产环境配置（可根据实际部署调整）
  // 生产环境配置（通过Nginx代理统一域名）
  production: {
    BACKEND_URL: 'https://your-domain.com',
    API_BASE_URL: '/api/v1',  // 生产环境也使用相对路径，通过Nginx代理
    FRONTEND_URL: 'https://your-domain.com',
    USE_PROXY: false  // 生产环境使用Nginx代理，不是Vite代理
  },
  
  // 测试环境配置
  testing: {
    BACKEND_URL: 'https://test.your-domain.com',
    API_BASE_URL: '/api/v1',  // 测试环境也通过代理访问
    FRONTEND_URL: 'https://test.your-domain.com',
    USE_PROXY: false  // 测试环境使用Nginx代理
  }
}

// 获取当前环境配置（运行时使用）
const getCurrentEnvironment = () => {
  // 检查是否在浏览器环境中
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    const mode = import.meta.env.MODE || 'development'
    const config = ENVIRONMENT_CONFIG[mode] || ENVIRONMENT_CONFIG.development
    
    // 如果有环境变量，优先使用环境变量
    if (import.meta.env.VITE_BACKEND_URL) {
      return {
        ...config,
        BACKEND_URL: import.meta.env.VITE_BACKEND_URL,
        API_BASE_URL: import.meta.env.VITE_API_BASE_URL || `${import.meta.env.VITE_BACKEND_URL}/api/v1`,
        FRONTEND_URL: import.meta.env.VITE_FRONTEND_URL || config.FRONTEND_URL
      }
    }
    
    return config
  }
  
  // 默认返回开发环境配置（用于vite.config.js等配置文件）
  return ENVIRONMENT_CONFIG.development
}

// 当前环境配置
const currentEnv = getCurrentEnvironment()

// API基础配置
export const API_CONFIG = {
  // 当前环境
  ENVIRONMENT: typeof import.meta !== 'undefined' && import.meta.env ? (import.meta.env.MODE || 'development') : 'development',
  
  // 基础URL配置
  BASE_URL: currentEnv.API_BASE_URL,
  BACKEND_BASE_URL: currentEnv.BACKEND_URL,
  FRONTEND_BASE_URL: currentEnv.FRONTEND_URL,
  
  // 是否使用代理
  USE_PROXY: currentEnv.USE_PROXY,
  
  // 媒体文件和静态文件路径
  MEDIA_URL: '/media',
  STATIC_URL: '/static',
  
  // 请求超时时间
  TIMEOUT: 15000,
  
  // AI生成请求的超时时间（更长，2分钟）
  AI_TIMEOUT: 120000,
  
  // 错误消息配置
  ERROR_MESSAGES: {
    NETWORK_ERROR: '网络连接失败，请检查网络或后端服务是否启动',
    TIMEOUT_ERROR: '请求超时，请稍后重试',
    SERVER_ERROR: '服务器错误，请稍后重试',
    UNAUTHORIZED: '登录已过期，请重新登录',
    FORBIDDEN: '权限不足，无法访问',
    NOT_FOUND: '请求的资源不存在',
    VALIDATION_ERROR: '数据验证失败'
  },
  
  // URL构建辅助方法
  buildMediaUrl: (path) => {
    if (!path) return null
    
    // 开发环境使用代理，直接返回相对路径
    if (API_CONFIG.USE_PROXY) {
      return `${API_CONFIG.MEDIA_URL}${path.startsWith('/') ? '' : '/'}${path}`
    }
    
    // 生产环境返回完整URL
    return `${API_CONFIG.BACKEND_BASE_URL}${API_CONFIG.MEDIA_URL}${path.startsWith('/') ? '' : '/'}${path}`
  },
  
  buildStaticUrl: (path) => {
    if (!path) return null
    
    if (API_CONFIG.USE_PROXY) {
      return `${API_CONFIG.STATIC_URL}${path.startsWith('/') ? '' : '/'}${path}`
    }
    
    return `${API_CONFIG.BACKEND_BASE_URL}${API_CONFIG.STATIC_URL}${path.startsWith('/') ? '' : '/'}${path}`
  },
  
  // 构建完整的后端URL
  buildBackendUrl: (path) => {
    if (!path) return API_CONFIG.BACKEND_BASE_URL
    return `${API_CONFIG.BACKEND_BASE_URL}${path.startsWith('/') ? '' : '/'}${path}`
  },
  
  // 构建API URL
  buildApiUrl: (endpoint) => {
    if (!endpoint) return API_CONFIG.BASE_URL
    return `${API_CONFIG.BASE_URL}${endpoint.startsWith('/') ? '' : '/'}${endpoint}`
  }
}

// 用户认证相关API端点
export const AUTH_ENDPOINTS = {
  LOGIN: '/users/auth/login/',
  LOGOUT: '/users/auth/logout/',
  REFRESH_TOKEN: '/users/auth/refresh/',
  USER_PROFILE: '/users/profile/',
  CHANGE_PASSWORD: '/users/password/change/',
  CHANGE_USERNAME: '/users/username/change/',
  USER_LIST: '/users/manage/',
  USER_DETAIL: '/users/manage/:id/',
  REPRESENTATIVE_LIST: '/users/representatives/',
  STAFF_LIST: '/users/staff/'
}

// 履职管理相关API端点
export const PERFORMANCE_ENDPOINTS = {
  LIST: '/performance/records/',
  DETAIL: '/performance/records/:id/',
  CREATE: '/performance/records/',
  UPDATE: '/performance/records/:id/',
  DELETE: '/performance/records/:id/',
  ATTACHMENTS: '/performance/attachments/',
  ATTACHMENT_DETAIL: '/performance/attachments/:id/'
}

// 意见建议管理相关API端点
export const OPINION_ENDPOINTS = {
  LIST: '/opinions/suggestions/',
  DETAIL: '/opinions/suggestions/:id/',
  CREATE: '/opinions/suggestions/create/',
  UPDATE: '/opinions/suggestions/:id/update/',
  DELETE: '/opinions/suggestions/:id/delete/',
  SUBMIT: '/opinions/suggestions/:id/submit/',
  REVIEW: '/opinions/suggestions/:id/review/',
  AI_GENERATE: '/aiknowledge/opinion/generate/',
  STATISTICS: '/opinions/statistics/'
}

// AI知识库相关API端点
export const AIKNOWLEDGE_ENDPOINTS = {
  CHAT_SSE: '/aiknowledge/chat/sse/',
  AUDIO_TO_TEXT: '/aiknowledge/audio-to-text/',
  OPINION_GENERATE: '/aiknowledge/opinion/generate/',
  OPINION_GENERATE_SSE: '/aiknowledge/opinion/generate/sse/',
  VOICE_TO_OPINION: '/aiknowledge/voice-to-opinion/'
}

// HTTP状态码映射
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  VALIDATION_ERROR: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
}

// 本地存储键名
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'userInfo'
}

// 获取代理配置（专门为vite.config.js使用，避免环境变量问题）
export const getProxyConfig = (mode = 'development') => {
  const config = ENVIRONMENT_CONFIG[mode] || ENVIRONMENT_CONFIG.development
  
  return {
    '/media': {
      target: config.BACKEND_URL,
      changeOrigin: true,
      secure: false,
      timeout: 180000, // 3分钟超时，确保比前端长
      proxyTimeout: 180000 // 代理超时
    },
    '/api': {
      target: config.BACKEND_URL,
      changeOrigin: true,
      secure: false,
      timeout: 180000, // 3分钟超时，确保比前端长
      proxyTimeout: 180000 // 代理超时
    }
  }
}

// 代理配置（供vite.config.js使用）
export const PROXY_CONFIG = getProxyConfig() 