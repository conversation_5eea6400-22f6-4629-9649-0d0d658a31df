<template>
  <div class="representative-performance-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>代表履职详情</h2>
      <p>查看和管理所有代表的履职记录情况</p>
    </div>



    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-card>
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="时间范围">
            <el-select
              v-model="filterForm.time_filter"
              placeholder="选择时间"
              @change="handleFilterChange"
              style="width: 150px;"
            >
              <el-option
                key="time-month"
                label="本月"
                value="month">
              </el-option>
              <el-option
                key="time-quarter"
                label="本季度"
                value="quarter">
              </el-option>
              <el-option
                key="time-year"
                label="本年度"
                value="year">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="履职类型">
            <el-select
              v-model="filterForm.performance_type"
              placeholder="选择类型"
              clearable
              @change="handleFilterChange"
              style="width: 200px;"
            >
              <el-option
                v-for="type in performanceTypes"
                :key="`type-${type.value}`"
                :label="type.label"
                :value="type.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="搜索代表">
            <el-input 
              v-model="filterForm.search" 
              placeholder="输入代表姓名" 
              clearable 
              @input="handleSearchChange"
              style="width: 200px;">
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="loadRepresentativesList">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 代表卡片列表 -->
    <div class="representatives-section">
      <el-card>
        <template #header>
          <div class="section-header">
            <span>代表履职概览</span>
            <el-tag type="info">{{ representativesList.length }} 位代表</el-tag>
          </div>
        </template>
        
        <div v-loading="loading" class="representatives-grid">
          <div 
            v-for="rep in representativesList" 
            :key="rep.id"
            class="representative-card"
            @click="viewRepresentativeDetails(rep.id)"
          >
            <div class="rep-avatar">
              <img
                v-if="hasLoadedAvatar(rep)"
                :src="getAvatarUrl(rep)"
                :alt="rep.name"
                @error="handleUserAvatarError($event, rep)"
                class="avatar-img"
              />
              <img
                v-else-if="shouldShowDefaultAvatar(rep)"
                :src="defaultAvatar"
                :alt="rep.name"
                @error="handleDefaultAvatarError($event, rep)"
                class="avatar-img"
              />
              <div v-else class="avatar-placeholder">
                {{ rep.name.charAt(0) }}
              </div>
            </div>
            <div class="rep-info">
              <h3>{{ rep.name }}</h3>
              <div class="rep-details">
                <p class="rep-level">{{ rep.level }}</p>
                <p class="rep-position">{{ rep.current_position || '代表' }}</p>
                <p class="rep-phone" v-if="rep.mobile_phone">
                  <el-icon><Phone /></el-icon>
                  {{ rep.mobile_phone }}
                </p>
              </div>
              <div class="rep-stats">
                <div class="stat-item">
                  <span class="stat-value">{{ rep.record_count }}</span>
                  <span class="stat-desc">{{ getTimeFilterText() }}履职</span>
                </div>
                <div class="stat-item">
                  <span class="stat-value">{{ rep.year_record_count }}</span>
                  <span class="stat-desc">年度履职</span>
                </div>
              </div>
              <div class="rep-activity" v-if="rep.last_activity_date">
                <p class="activity-date">最近履职：{{ rep.last_activity_date }}</p>
                <p class="activity-content" :title="rep.last_activity_content">
                  {{ rep.last_activity_content }}
                </p>
                <el-tag size="small" type="primary">{{ rep.last_activity_type }}</el-tag>
              </div>
              <div class="rep-activity" v-else>
                <p class="no-activity">暂无履职记录</p>
              </div>
            </div>
            <div class="rep-actions">
              <el-button size="small" type="primary">
                <el-icon><View /></el-icon>
                查看详情
              </el-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <el-empty v-if="!loading && representativesList.length === 0" description="暂无代表数据">
          <el-button type="primary" @click="loadRepresentativesList">重新加载</el-button>
        </el-empty>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { staffPerformanceAPI, performanceChoicesAPI } from '@/api/modules/performance'
import httpClient from '@/api/http/client'
import defaultAvatarUrl from '@/assets/default_avatar.PNG'
import {
  Search,
  Refresh,
  View,
  Phone
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const representativesList = ref([])
const performanceTypes = ref([])

// 头像相关数据
const avatarCache = ref({})
const failedUserAvatars = ref(new Set())
const failedDefaultAvatars = ref(new Set())
const loadingAvatars = ref(new Set())
const loadQueue = ref([])
const isLoadingAvatar = ref(false)
const defaultAvatar = defaultAvatarUrl // 默认头像路径

// 筛选表单
const filterForm = reactive({
  time_filter: 'month',
  performance_type: '',
  search: ''
})

// 搜索防抖
let searchTimeout = null

// 计算属性
const getTimeFilterText = () => {
  const filterMap = {
    'month': '本月',
    'quarter': '本季度', 
    'year': '本年度'
  }
  return filterMap[filterForm.time_filter] || '本月'
}

// 方法
const loadRepresentativesList = async () => {
  try {
    loading.value = true
    const params = {
      time_filter: filterForm.time_filter,
      performance_type: filterForm.performance_type,
      search: filterForm.search
    }
    
    const response = await staffPerformanceAPI.getRepresentativesList(params)
    representativesList.value = response.data.results || []

    // 异步加载头像
    loadAvatarsAsync()

    console.log('代表履职列表:', representativesList.value)
  } catch (error) {
    console.error('加载代表列表失败:', error)
    ElMessage.error('加载代表列表失败')
  } finally {
    loading.value = false
  }
}

const loadPerformanceTypes = async () => {
  try {
    const response = await performanceChoicesAPI.getPerformanceTypes()
    performanceTypes.value = response.data.choices || []
  } catch (error) {
    console.error('加载履职类型失败:', error)
  }
}

const handleFilterChange = () => {
  loadRepresentativesList()
}

const handleSearchChange = () => {
  // 搜索防抖
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    loadRepresentativesList()
  }, 500)
}

const viewRepresentativeDetails = (representativeId) => {
  // 跳转到代表详细履职记录页面
  router.push(`/staff/representative-performance/${representativeId}`)
}

// 头像相关方法
const hasLoadedAvatar = (item) => {
  return avatarCache.value[item.id] && !failedUserAvatars.value.has(item.name)
}

const getAvatarUrl = (item) => {
  return avatarCache.value[item.id] || ''
}

const shouldShowDefaultAvatar = (item) => {
  return !failedDefaultAvatars.value.has(item.name)
}

const loadAvatarsAsync = async () => {
  // 构建加载队列 - 所有代表都尝试加载头像
  loadQueue.value = representativesList.value
    .filter(item => !avatarCache.value[item.id])
    .map(item => item.id)

  console.log(`开始加载 ${loadQueue.value.length} 个头像`)
  processAvatarQueue()
}

const processAvatarQueue = async () => {
  if (isLoadingAvatar.value || loadQueue.value.length === 0) {
    return
  }

  isLoadingAvatar.value = true
  const representativeId = loadQueue.value.shift()

  try {
    await loadSingleAvatar(representativeId)
  } catch (error) {
    console.error(`加载头像失败 ID:${representativeId}`, error)
  }

  isLoadingAvatar.value = false

  // 继续处理下一个
  if (loadQueue.value.length > 0) {
    setTimeout(() => {
      processAvatarQueue()
    }, 100) // 100ms间隔，避免请求过于频繁
  }
}

const loadSingleAvatar = async (representativeId) => {
  if (loadingAvatars.value.has(representativeId)) {
    return // 避免重复加载
  }

  loadingAvatars.value.add(representativeId)

  try {
    const response = await httpClient({
      url: `/bigscreen/avatar/${representativeId}/`,
      method: 'get',
      responseType: 'blob',
      showLoading: false,
      // 静默处理错误，不显示错误提示
      validateStatus: function (status) {
        // 200-299范围的状态码都认为是成功
        // 404也不抛出错误，静默处理
        return (status >= 200 && status < 300) || status === 404
      }
    })

    // 检查响应状态
    if (response.status === 404) {
      // 代表没有上传头像，静默处理
      throw new Error('NO_AVATAR')
    }

    // 将blob转换为URL
    const avatarUrl = URL.createObjectURL(response.data)
    avatarCache.value[representativeId] = avatarUrl

    // 只在开发环境显示成功日志
    if (import.meta.env.DEV) {
      console.log(`头像加载成功 ID:${representativeId}`)
    }
  } catch (error) {
    // 静默处理没有头像的情况
    if (error.message === 'NO_AVATAR') {
      // 完全静默，不输出任何日志
    } else {
      // 其他错误才在开发环境显示
      if (import.meta.env.DEV) {
        console.log(`头像加载失败 ID:${representativeId}`, error.message)
      }
    }

    // 标记为失败，避免重复尝试
    const representative = representativesList.value.find(item => item.id === representativeId)
    if (representative) {
      failedUserAvatars.value.add(representative.name)
    }
  } finally {
    loadingAvatars.value.delete(representativeId)
  }
}

const handleUserAvatarError = (event, item) => {
  console.log(`用户头像显示失败: ${item.name}`)
  failedUserAvatars.value.add(item.name)
  event.target.style.display = 'none'
}

const handleDefaultAvatarError = (event, item) => {
  console.log(`默认头像加载失败: ${item.name}`)
  failedDefaultAvatars.value.add(item.name)
  event.target.style.display = 'none'
}

// 生命周期
onMounted(() => {
  loadRepresentativesList()
  loadPerformanceTypes()
})
</script>

<style scoped>
.representative-performance-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: var(--text-color);
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-form {
  margin: 0;
}

.filter-form :deep(.el-form-item) {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.representatives-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.representative-card {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.representative-card:hover {
  border-color: var(--china-red);
  box-shadow: 0 4px 12px rgba(200, 16, 46, 0.2);
  transform: translateY(-2px);
}

.rep-avatar {
  margin-right: 16px;
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--china-red);
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: bold;
  background-color: var(--china-red);
}

.rep-info {
  flex: 1;
  min-width: 0;
}

.rep-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.rep-details {
  margin-bottom: 12px;
}

.rep-level, .rep-position {
  margin: 0 0 4px 0;
  font-size: 13px;
  color: #666;
}

.rep-phone {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 4px;
}

.rep-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  min-width: 60px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: var(--china-red);
  line-height: 1;
}

.stat-desc {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.rep-activity {
  margin-bottom: 8px;
}

.activity-date {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #666;
}

.activity-content {
  margin: 0 0 6px 0;
  font-size: 13px;
  color: var(--text-color);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.no-activity {
  margin: 0;
  font-size: 13px;
  color: #999;
  font-style: italic;
}

.rep-actions {
  margin-left: 12px;
  flex-shrink: 0;
}

.detail-btn {
  background: white;
  border: 1px solid #dcdfe6;
  color: var(--china-red);
}

.detail-btn:hover {
  background: var(--china-red);
  border-color: var(--china-red);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .representative-performance-page {
    padding: 10px;
  }
  
  .representatives-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .representative-card {
    padding: 15px;
  }
  
  .rep-stats {
    gap: 12px;
  }
  
  .stat-item {
    min-width: 50px;
    padding: 6px 8px;
  }
  
  .filter-form {
    flex-direction: column;
  }
  
  .filter-form :deep(.el-form-item) {
    margin-bottom: 15px;
  }
}
</style>
